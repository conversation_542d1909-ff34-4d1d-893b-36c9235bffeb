import { useWebSocket, useBroadcastChannel } from "@vueuse/core";
import { ref } from "vue";
import { useAsyncStoreManager } from "./store";
import { useUserStore } from "@/stores/modules/user";
import { useMapChangeChannel } from "@/views/map/channel";
import { getCurrentLatestMap } from "@/api/modules/dispatch";
import { useDeviceData } from "@/composables/useDeviceData";
import type { WSDeviceInitData } from "@/stores/deviceTypes";
import { deviceTestScript } from "@/utils/deviceTestScript";
// WebSocket 消息类型定义
interface WebSocketMessage {
  messageType: string;
  socketType: string;
  token?: string;
  data?: any;
  [key: string]: any;
}

// 创建全局 WebSocket 连接管理
export const useWebSocketManager = () => {
  const userStore = useUserStore();
  const { post } = useBroadcastChannel({ name: "map-channel" });
  const { refreshAll, getStore } = useAsyncStoreManager();
  const { updateFromWebSocket, updateSingleDevice } = useDeviceData();
  const isConnected = ref(false);
  const lastHeartbeat = ref<Date | null>(null);

  // setInterval(() => {
  //   useMapChangeChannel().post({ type: "map-change", data: new Date().toISOString() });
  // }, 3000);

  // 处理所有WebSocket消息
  const handleWebSocketMessage = (data: WebSocketMessage) => {
    console.log("[WebSocket] Received message:", data);
    switch (data.messageType) {
      case "mapChange":
        // 地图数据变化，刷新所有数据
        getCurrentLatestMap().then(res => {
          // @ts-ignore
          if (res.data?.filePath) useMapChangeChannel().post({ type: "map-change", data: res.data.filePath });
        });
        break;

      case "pong":
        // 心跳响应
        lastHeartbeat.value = new Date();
        break;

      case "deviceInit":
        // 设备初始化数据，更新设备管理器
        if (data.data) {
          console.log("[WebSocket] Updating devices from deviceInit:", data.data);
          updateFromWebSocket(data.data as WSDeviceInitData);
        }
        break;

      case "trainMessage":
        // 单个矿车数据更新
        if (data.data && data.data.code) {
          console.log("[WebSocket] Updating single mineTrain:", data.data);
          updateSingleDevice(data.data.code, {
            ...data.data,
            deviceType: "mineTrain" as const
          });
        }
        break;

      case "bulldozersMessage":
        // 单个挖机数据更新
        if (data.data && data.data.code) {
          console.log("[WebSocket] Updating single bulldozer:", data.data);
          updateSingleDevice(data.data.code, {
            ...data.data,
            deviceType: "bulldozer" as const
          });
        }
        break;

      case "obstacleMessageAll":
        // 障碍物更新
        const taskStore = getStore("dispatchObstacle");
        taskStore?.refresh();
        break;

      case "dispatchLog":
        // 运行日志状态更新
        const dispatchLogStore = getStore("deviceDispatchLogs");
        dispatchLogStore?.refresh();
        break;

      case "faultsMessage":
        // 故障状态更新
        const faultStore = getStore("deviceDispatchFaults");
        faultStore?.refresh();
        break;

      default:
        console.warn("[WebSocket] Unknown message type:", data.messageType);
    }
  };
  // 创建 WebSocket 连接
  const { status, data, send, open, close } = useWebSocket(
    `ws://192.168.0.41:7010/conn/webSocket?token=${userStore.token}&type=unmannedPlatform`,
    {
      autoReconnect: {
        retries: 2,
        delay: 1000,
        onFailed() {
          console.error("[WebSocket] Reconnection failed");
        }
      },
      heartbeat: {
        message: JSON.stringify({ messageType: "ping", socketType: "unmannedPlatform" }),
        interval: 30000,
        pongTimeout: 1000
      },
      onConnected() {
        console.log("[WebSocket] Connected");
        isConnected.value = true;
        sendMessage({ messageType: "deviceInit", socketType: "unmannedPlatform" });
      },
      onDisconnected() {
        console.log("[WebSocket] Disconnected");
        isConnected.value = false;
      },
      onError(error) {
        console.error("[WebSocket] Error:", error);
      },
      onMessage(ws: WebSocket, message: MessageEvent) {
        try {
          const data = JSON.parse(message.data) as WebSocketMessage;
          handleWebSocketMessage(data);
        } catch (error) {
          console.error("[WebSocket] Failed to parse message:", error);
        }
      }
    }
  );
  // 发送消息的包装函数
  const sendMessage = (message: WebSocketMessage) => {
    if (status.value === "OPEN") {
      console.log("[WebSocket] Sending message:", message);
      send(JSON.stringify(message));
    } else {
      console.warn("[WebSocket] Not connected, message not sent:", message);
    }
  };

  // 手动重连
  const reconnect = async () => {
    console.log("[WebSocket] Manual reconnection initiated");
    await close();
    open();
  };

  // 初始化测试脚本，让它可以调用handleWebSocketMessage
  deviceTestScript.setMessageHandler(handleWebSocketMessage);

  // 在开发环境下自动暴露测试方法到全局
  if (process.env.NODE_ENV === "development") {
    (window as any).wsTest = {
      handleMessage: handleWebSocketMessage,
      testScript: deviceTestScript,
      sendTestInit: () => deviceTestScript.sendDeviceInit(),
      sendTestTrain: () => deviceTestScript.sendTrainMessage(),
      sendTestBulldozer: () => deviceTestScript.sendBulldozerMessage(),
      startAutoTest: () => deviceTestScript.start(),
      stopAutoTest: () => deviceTestScript.stop()
    };

    console.log(`
🧪 WebSocket测试工具已加载！

在浏览器控制台中使用：
- wsTest.sendTestInit()     // 发送测试初始化数据
- wsTest.sendTestTrain()    // 发送测试矿车数据
- wsTest.sendTestBulldozer() // 发送测试挖机数据
- wsTest.startAutoTest()    // 开始自动测试
- wsTest.stopAutoTest()     // 停止自动测试

或者使用全局测试脚本：
- deviceTest.start()        // 开始测试
- deviceTest.stop()         // 停止测试
    `);
  }

  return {
    isConnected,
    lastHeartbeat,
    status,
    data,
    sendMessage,
    reconnect,
    // 暴露测试方法（仅开发环境）
    ...(process.env.NODE_ENV === "development" && {
      testScript: deviceTestScript,
      handleTestMessage: handleWebSocketMessage
    })
  };
};

// 创建全局单例
const wsManager = useWebSocketManager();

// 导出 sendMessage 函数，确保 WebSocket 已连接
export const sendMessage = (message: WebSocketMessage) => {
  return wsManager.sendMessage(message);
};
