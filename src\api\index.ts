import axios, { AxiosInstance, AxiosError, AxiosRequestConfig, InternalAxiosRequestConfig, AxiosResponse } from "axios";
import { showFullScreenLoading, tryHideFullScreenLoading } from "@/components/Loading/fullScreen";
import { LOGIN_URL } from "@/config";
import { ElMessage } from "element-plus";
import { ResultData } from "@/api/interface";
import { ContentTypeEnum, ResponseStatus, ResultEnum } from "@/enums/httpEnum";
import { checkStatus } from "./helper/checkStatus";
import { AxiosCanceler } from "./helper/axiosCancel";
import { useUserStore } from "@/stores/modules/user";
import router from "@/routers";
import qs from "qs";
import { removeEmptyValues } from "@/utils/index.ts";
export interface CustomAxiosRequestConfig extends InternalAxiosRequestConfig {
  loading?: boolean;
  cancel?: boolean;
  responseError?: boolean; // 新增错误响应处理控制字段
  removeEmptyString?: boolean; // 新增参数空值去除控制字段
}
const config = {
  // 默认地址请求地址，可在 .env.** 文件中修改
  baseURL: import.meta.env.VITE_API_URL as string,
  // 设置超时时间
  timeout: ResultEnum.TIMEOUT as number,
  // 跨域时候允许携带凭证
  withCredentials: true,
  // 请求时默认携带的headers
  headers: {
    Accept: "application/json, text/plain",
    "Content-Type": ContentTypeEnum.FORM_URLENCODED
  },
  // 用于对参数进行序列化
  paramsSerializer: params => qs.stringify(params, { indices: false })
};

const axiosCanceler = new AxiosCanceler();

class RequestHttp {
  service: AxiosInstance;

  public constructor(config: AxiosRequestConfig) {
    // instantiation
    this.service = axios.create(config);

    /**
     * @description 请求拦截器
     * 客户端发送请求 -> [请求拦截器] -> 服务器
     * token校验(JWT) : 接受服务器返回的 token,存储到 vuex/pinia/本地储存当中
     */
    this.service.interceptors.request.use(
      (config: CustomAxiosRequestConfig) => {
        const userStore = useUserStore();
        // removeEmptyString参数默认去除空值（只处理key:''） 而key的类型有可能有key:[] ,key:{}(递归去除'')
        config.removeEmptyString ??= true;
        // 深度清理参数（处理data）
        if (config.removeEmptyString) {
          const deepClean = (target: any) => {
            if (typeof target === "object" && target !== null) {
              return removeEmptyValues(target); // 使用新函数
            }
            return target;
          };
          config.data &&= deepClean(config.data);
        }

        // 重复请求不需要取消，在 api 服务中通过指定的第三个参数: { cancel: false } 来控制
        config.cancel ??= true;
        config.cancel && axiosCanceler.addPending(config);
        // 当前请求不需要显示 loading，在 api 服务中通过指定的第三个参数: { loading: false } 来控制
        config.loading ??= true;
        config.loading && showFullScreenLoading();
        if (config.headers && typeof config.headers.set === "function" && userStore.token) {
          config.headers.set("Authorization", "Bearer " + userStore.token);
        }

        return config;
      },
      (error: AxiosError) => {
        return Promise.reject(error);
      }
    );

    /**
     * @description 响应拦截器
     *  服务器换返回信息 -> [拦截统一处理] -> 客户端JS获取到信息
     */
    this.service.interceptors.response.use(
      (response: AxiosResponse & { config: CustomAxiosRequestConfig }) => {
        const { data, config } = response;
        const userStore = useUserStore();
        axiosCanceler.removePending(config);
        config.loading && tryHideFullScreenLoading();
        // 对响应中的状态码进行判断执行对应的操作
        const responseStatus = response.data.statusCode;
        switch (responseStatus) {
          // 用户未登录
          case ResponseStatus.USER_NEED_AUTHORITIES:
          // token为黑名单
          // eslint-disable-next-line no-fallthrough
          case ResponseStatus.TOKEN_IS_BLACKLIST:
          // 登录失效
          // eslint-disable-next-line no-fallthrough
          case ResponseStatus.LOGIN_IS_OVERDUE:
          // 获取当前用户失败
          // eslint-disable-next-line no-fallthrough
          case ResponseStatus.FEIGN_GET_USER_FAILED:
            // case ResponseStatus.SUCCESS:
            userStore.setToken("");
            // ElMessage.error("登录失效，请重新登录");
            // setTimeout(() => {
            //   window.location.reload();
            // }, 800);
            break;
        }
        // 没权限
        if (data.statusCode == ResultEnum.OVERDUE) {
          userStore.setToken("");
          router.replace(LOGIN_URL);
          ElMessage.error(data.message);
          return Promise.reject(data);
        }
        // 全局错误信息拦截（防止下载文件的时候返回数据流，没有 code 直接报错）
        if (
          data.statusCode &&
          ![ResultEnum.success, ResultEnum.SUCCESS, ResponseStatus.USER_LOGOUT_SUCCESS].includes(data.statusCode)
        ) {
          ElMessage.error(data.message);
          return Promise.reject(data);
        }
        // 成功请求（在页面上除非特殊情况，否则不用处理失败逻辑）
        return data;
      },
      async (error: AxiosError) => {
        const { response, config } = error;
        tryHideFullScreenLoading();
        // 3. 获取请求配置中的错误处理标识
        const customConfig = config as CustomAxiosRequestConfig | undefined;
        const shouldHandleError = customConfig?.responseError ?? true; // 默认处理错误
        // 4. 根据配置决定是否执行错误处理
        if (shouldHandleError) {
          // 请求超时 && 网络错误单独判断，没有 response
          if (error.message.indexOf("timeout") !== -1) ElMessage.error("请求超时！请您稍后重试");
          if (error.message.indexOf("Network Error") !== -1) ElMessage.error("网络错误！请您稍后重试");
          // 根据服务器响应的错误状态码，做不同的处理
          // @ts-ignore
          // if (error?.toJSON()?.status === 401) {
          //   const userStore = useUserStore();
          // 2.清除 Token、清除信息
          // userStore.setToken("");
          // setTimeout(() => {
          //   window.location.reload();
          // }, 800);
          // }

          if (response) checkStatus(response);
        }
        // 服务器结果都没有返回(可能服务器错误可能客户端断网)，断网处理:可以跳转到断网页面
        if (!window.navigator.onLine) await router.replace("/500");
        return Promise.reject(error);
      }
    );
  }

  /**
   * @description 常用请求方法封装
   */
  get<T>(url: string, params?: object, _object = {}): Promise<ResultData<T>> {
    return this.service.get(url, { params, ..._object });
  }

  post<T>(url: string, params?: object | string, _object = {}): Promise<ResultData<T>> {
    return this.service.post(url, params, _object);
  }

  put<T>(url: string, params?: object, _object = {}): Promise<ResultData<T>> {
    return this.service.put(url, params, _object);
  }

  delete<T>(url: string, params?: any, _object = {}): Promise<ResultData<T>> {
    return this.service.delete(url, { params, ..._object });
  }

  download(url: string, params?: object, _object = {}): Promise<BlobPart> {
    return this.service.post(url, params, { ..._object, responseType: "blob" });
  }
}

export default new RequestHttp(config);
