<template>
  <el-row>
    <el-text size="large" tag="b"> {{ title }}</el-text>
  </el-row>

  <template v-for="row of rows">
    <template v-if="row.type === 'status'">
      <el-row :gutter="7">
        <el-col v-for="item of row.data" :key="item.key" :span="24 / row.data.length">
          <div class="card-container" :style="{ height: row.height + 'px', 'align-items': 'flex-start' }">
            <span :style="{ color: item.color, fontSize: '20px', fontWeight: 'bold' }">{{ item.value }}</span>
            <el-text size="small">{{ item.label }}</el-text>
          </div>
        </el-col>
      </el-row>
    </template>
    <template v-else-if="row.type === 'device-space'">
      <el-scrollbar v-if="row.data.length" :style="{ width: '100%', height: row.height + 'px' }">
        <div style="gap: 8px; display: flex; flex-wrap: wrap">
          <div
            class="card-container"
            :class="{ active: isDeviceActive(item) }"
            @click="handleDeviceClick(item)"
            v-for="item of row.data"
            :key="item.code"
            style="height: 74px; width: 142px; padding: 4px"
          >
            <div class="error-flag" v-if="item.isFault"></div>
            <div class="flex-justify-between" style="width: 100%">
              <div style="display: flex; flex-wrap: wrap; padding-left: 12px; overflow: hidden">
                <div
                  style="display: inline-flex; align-items: center; flex-wrap: nowrap"
                  :style="{
                    color: item.isFault
                      ? 'var(--el-color-error)'
                      : item.rawData.isActivateTheme
                        ? 'var(--el-color-primary)'
                        : 'inherit'
                  }"
                >
                  <span :style="{ whiteSpace: 'nowrap', fontSize: '18px', fontWeight: 'bold' }">
                    {{ item.name }}
                  </span>
                  <div class="tag" v-if="item.showMode && item.rawData.isActivateTheme">
                    <el-text size="small" style="color: inherit"> {{ item.modeText }}</el-text>
                  </div>
                </div>
                <el-text
                  size="default"
                  style="width: 100%"
                  :style="{ color: item.isFault ? 'var(--el-color-error)' : 'inherit' }"
                >
                  {{ item.statusText }}
                </el-text>
              </div>
              <div class="battery" v-if="item.showBattery">
                <div class="content-box" :class="item.lowBattery && 'low-battery'">
                  <SvgIcon
                    name="IconCharging"
                    icon-style="width:14px; height:26px;"
                    :color="item.lowBattery ? 'var(--el-color-error)' : 'var(--el-color-primary)'"
                  />
                  <el-text style="color: inherit" size="small">{{ item.battery }}</el-text>
                </div>
                <div class="capacity" :style="{ height: item.battery + '%' }"></div>
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>
      <el-empty image-size="0" v-else description="暂无数据" />
    </template>
  </template>
</template>

<script lang="ts" setup>
interface DeviceStatusData {
  value: string | number;
  label: string;
  key: string;
  color: string;
}

interface DeviceSpaceData {
  name: string;
  code: string;
  modeText: string;
  battery: number;
  status: string | number;
  statusText: string;
  isFault: boolean;
  lowBattery: boolean;
  showMode: boolean;
  showBattery: boolean;
  deviceType: string;
  rawData: {
    id: string;
    name: string;
    code: string;
    status: string | number;
    modelNumber: string | null;
    [key: string]: any;
  };
}

interface DeviceStatusRowSpace {
  type: "status";
  height: number;
  data: DeviceStatusData[];
}

interface DeviceRowSpace {
  type: "device-space";
  height: number;
  data: DeviceSpaceData[];
}

interface DeviceSpace {
  title: string;
  rows: (DeviceStatusRowSpace | DeviceRowSpace)[];
}

import SvgIcon from "@/components/SvgIcon/index.vue";
import { useDeviceSelection } from "@/views/copilot/store";
import { useDeviceDetail } from "@/views/copilot/request";
import { sendMessage } from "@/views/copilot/socket";

defineProps<DeviceSpace>();

const { selectDevice, selectedDeviceId, clearSelection } = useDeviceSelection();
const { data, refresh } = useDeviceDetail();

const handleDeviceClick = async (item: DeviceSpaceData) => {
  // 如果点击的是当前选中的设备，则取消选中
  if (item.rawData.id === selectedDeviceId.value) {
    clearSelection();
    sendMessage({
      socketType: "unmannedPlatform",
      messageType: "unselect",
      deviceCode: item.rawData?.rawData?.deviceCode
    });
    return;
  }

  // 先选中设备基本信息
  const deviceInfo = {
    id: item.rawData.id,
    type: item.deviceType,
    name: item.rawData.name,
    status: Number(item.rawData.status),
    rawData: item.rawData
  };

  // 先选中设备，这样 useDeviceDetail 会开始获取新设备的详情
  selectDevice(deviceInfo);

  try {
    // 等待设备详情数据刷新
    await refresh();

    // 更新设备信息，包含最新的详情数据
    selectDevice({
      ...deviceInfo,
      detail: data.value,
      rawData: { ...item.rawData, ...data.value }
    });
    sendMessage({
      socketType: "unmannedPlatform",
      messageType: "chooseDevice",
      deviceCode: item.rawData.deviceCode
    });
  } catch (error) {
    console.error("Failed to fetch device detail:", error);
    // 即使获取详情失败，设备也已经被选中，只是没有详情数据
  }
};

const isDeviceActive = (item: DeviceSpaceData) => {
  return item.rawData.id === selectedDeviceId.value;
};
</script>

<style lang="scss" scoped>
.card-container {
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  cursor: pointer;
  position: relative;
  &:hover,
  &.active {
    background: linear-gradient(90deg, #024367 0%, #191a47 100%);
    border: 1px solid rgba(255, 255, 255, 0.5);
  }
}
.error-flag {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  background: url("../../imgs/error-flag.png") no-repeat center center;
  background-size: 100% 100%;
  width: 20px;
  height: 20px;
}
.tag {
  width: 30px;
  height: 18px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--el-color-primary);
  background: var(--el-color-primary-opacity-1);
  &.error {
    color: var(--el-color-error);
    background: var(--el-color-error-opacity-1);
  }
}
.battery {
  width: 28px;
  height: 66px;
  flex: none;
  background: var(--el-bg-color);
  position: relative;
  border-radius: 4px;
  .content-box {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 2;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    &.low-battery {
      color: var(--el-color-error);
    }
  }
  .capacity {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    bottom: 0;
    border-radius: 4px;
    background: rgba(46, 48, 112, 1);
  }
}
</style>
