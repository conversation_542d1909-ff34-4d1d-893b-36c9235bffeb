import { ref, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import type { TrackData, OfflineDevice, VehicleInfo, RawTrackPoint, TrackDataResponse, Position } from "../types";
import { getDispatchTrackData } from "@/api/modules/dispatch";

export function useTrackPlayback() {
  // 响应式状态
  const selectedVehicles = ref<string[]>([]);
  const selectedDate = ref<string>("");
  const currentTime = ref<number>(0);
  const isPlaying = ref<boolean>(false);
  const playSpeed = ref<number>(1);
  const trackData = ref<TrackData[]>([]);
  const offlineDevices = ref<OfflineDevice[]>([]);
  const loading = ref<boolean>(false);
  const totalDuration = ref<number>(0);
  const startTime = ref<number>(0);
  const endTime = ref<number>(0);
  const showTimelineControl = ref<boolean>(false);

  // 播放定时器
  let playTimer: number | null = null;
  let cesiumViewer: any = null;

  // 计算属性
  const timelinePercent = computed({
    get: () => {
      if (totalDuration.value === 0) return 0;
      return ((currentTime.value - startTime.value) / totalDuration.value) * 100;
    },
    set: (percent: number) => {
      if (totalDuration.value > 0) {
        currentTime.value = startTime.value + (totalDuration.value * percent) / 100;
      }
    }
  });

  const hasTrackData = computed(() => trackData.value.length > 0);

  // 时间轴显示的开始和结束时间（基于选择的日期）
  const timelineStartTime = computed(() => {
    if (!selectedDate.value) return 0;
    const date = new Date(selectedDate.value);
    date.setHours(0, 0, 0, 0);
    return date.getTime();
  });

  const timelineEndTime = computed(() => {
    if (!selectedDate.value) return 0;
    const date = new Date(selectedDate.value);
    date.setHours(23, 59, 59, 999);
    return date.getTime();
  });

  // 格式化时间显示
  const formatTime = (timestamp: number): string => {
    if (!timestamp) return "00:00:00";
    const date = new Date(timestamp);
    return date.toLocaleTimeString("zh-CN", { hour12: false });
  };

  // 将原始轨迹数据转换为TrackData格式
  const convertRawDataToTrackData = (rawData: RawTrackPoint[]): TrackData[] => {
    // 按设备分组
    const deviceGroups = new Map<string, RawTrackPoint[]>();

    rawData.forEach(point => {
      if (!deviceGroups.has(point.deviceCode)) {
        deviceGroups.set(point.deviceCode, []);
      }
      deviceGroups.get(point.deviceCode)!.push(point);
    });

    const trackDataList: TrackData[] = [];

    deviceGroups.forEach((points, deviceCode) => {
      if (points.length === 0) return;

      // 按时间排序
      points.sort((a, b) => new Date(a.time).getTime() - new Date(b.time).getTime());

      // 转换位置数据
      const positions: Position[] = points.map(point => {
        const [longitude, latitude, altitude, heading] = point.currentLocation.split(" ").map(Number);
        return {
          timestamp: new Date(point.time).getTime(),
          longitude,
          latitude,
          altitude: altitude || 0,
          heading: heading || 0,
          speed: parseFloat(point.speed) || 0
        };
      });

      // 确定车辆类型（根据设备编码或名称判断）
      const vehicleType = deviceCode.includes("KK") || points[0].name.includes("KK") ? "truck" : "excavator";

      trackDataList.push({
        id: deviceCode,
        name: points[0].name,
        type: vehicleType,
        startTime: timelineStartTime.value, // 固定为选择日期的00:00:00
        endTime: timelineEndTime.value, // 固定为选择日期的23:59:59
        positions,
        status: "online"
      });
    });

    return trackDataList;
  };

  // 处理回放按钮点击
  const handleReplay = async () => {
    if (!selectedVehicles.value.length) {
      ElMessage.warning("请选择要回放的车辆");
      return;
    }

    if (!selectedDate.value) {
      ElMessage.warning("请选择回放日期");
      return;
    }

    loading.value = true;

    try {
      // 调用真实API
      const apiResponse = await getDispatchTrackData({
        deviceCodes: selectedVehicles.value.join(","),
        startTime: selectedDate.value + " 00:00:00",
        endTime: selectedDate.value + " 23:59:59"
      });

      console.log("API响应数据:", apiResponse);

      // 检查API响应格式
      if (!apiResponse?.data || !Array.isArray(apiResponse?.data)) {
        ElMessage.warning("API返回数据格式错误");
        return;
      }

      if (apiResponse?.data?.length === 0) {
        ElMessage.warning("没有找到选中车辆的轨迹数据");
        return;
      }

      // 转换数据格式
      const convertedTrackData = convertRawDataToTrackData(apiResponse?.data as RawTrackPoint[]);

      if (convertedTrackData.length === 0) {
        ElMessage.warning("轨迹数据转换失败");
        return;
      }

      // 设置轨迹数据
      trackData.value = convertedTrackData;

      // 设置固定的时间范围（选择日期的00:00:00到23:59:59）
      startTime.value = timelineStartTime.value;
      endTime.value = timelineEndTime.value;
      totalDuration.value = endTime.value - startTime.value;
      currentTime.value = startTime.value;

      // 更新离线设备
      updateOfflineDevices();

      // 显示时间轴控制
      showTimelineControl.value = true;

      ElMessage.success(`轨迹数据加载成功，共${convertedTrackData.length}个设备`);
    } catch (error) {
      console.error("加载轨迹数据失败:", error);
      ElMessage.error("加载轨迹数据失败");
    } finally {
      loading.value = false;
    }
  };

  // 处理重置按钮点击
  const handleReset = () => {
    // 停止播放
    if (isPlaying.value) {
      togglePlayback();
    }

    // 重置所有状态
    selectedVehicles.value = [];
    selectedDate.value = "";
    currentTime.value = 0;
    trackData.value = [];
    offlineDevices.value = [];
    totalDuration.value = 0;
    startTime.value = 0;
    endTime.value = 0;
    showTimelineControl.value = false;

    ElMessage.info("已重置所有设置");
  };

  // 处理时间轴变化
  const handleTimelineChange = (percent: number) => {
    timelinePercent.value = percent;
    updateOfflineDevices();

    // 同步Cesium时钟
    if (cesiumViewer) {
      cesiumViewer.clock.currentTime = Cesium.JulianDate.fromDate(new Date(currentTime.value));
    }
  };

  // 处理时间轴拖拽
  const handleTimelineDrag = (percent: number) => {
    timelinePercent.value = percent;
  };

  // 切换播放状态
  const togglePlayback = () => {
    if (!hasTrackData.value) return;

    isPlaying.value = !isPlaying.value;

    if (isPlaying.value) {
      startPlayback();
    } else {
      stopPlayback();
    }
  };

  // 开始播放
  const startPlayback = () => {
    if (playTimer) {
      clearInterval(playTimer);
    }

    // 根据播放倍速设置定时器间隔
    const interval = 1000 / playSpeed.value;

    playTimer = window.setInterval(() => {
      const nextTime = currentTime.value + 1000 * playSpeed.value;

      if (nextTime >= endTime.value) {
        // 播放结束
        currentTime.value = endTime.value;
        stopPlayback();
        ElMessage.info("轨迹回放完成");
      } else {
        currentTime.value = nextTime;
        updateOfflineDevices();
      }
    }, interval);

    // 同步Cesium播放状态
    if (cesiumViewer) {
      cesiumViewer.clock.shouldAnimate = true;
      cesiumViewer.clock.multiplier = playSpeed.value;
    }
  };

  // 停止播放
  const stopPlayback = () => {
    if (playTimer) {
      clearInterval(playTimer);
      playTimer = null;
    }

    isPlaying.value = false;

    // 同步Cesium播放状态
    if (cesiumViewer) {
      cesiumViewer.clock.shouldAnimate = false;
    }
  };

  // 更新离线设备列表
  const updateOfflineDevices = () => {
    if (!hasTrackData.value || currentTime.value === 0) {
      offlineDevices.value = [];
      return;
    }

    const offline: OfflineDevice[] = [];

    trackData.value.forEach(vehicle => {
      // 检查当前时间点车辆是否有位置数据
      const hasPositionAtCurrentTime = vehicle.positions.some(
        pos => Math.abs(pos.timestamp - currentTime.value) < 60000 // 1分钟内
      );

      if (!hasPositionAtCurrentTime) {
        offline.push({
          id: vehicle.id,
          name: vehicle.name,
          type: vehicle.type,
          offlineTime: currentTime.value,
          lastPosition: vehicle.positions[vehicle.positions.length - 1]
        });
      }
    });

    offlineDevices.value = offline;
  };

  // 处理地图准备就绪
  const handleMapReady = (viewer: any) => {
    cesiumViewer = viewer;
    console.log("地图初始化完成");
  };

  // 监听播放倍速变化
  watch(playSpeed, newSpeed => {
    if (isPlaying.value) {
      // 重新开始播放以应用新的倍速
      stopPlayback();
      isPlaying.value = true;
      startPlayback();
    }

    // 同步Cesium倍速
    if (cesiumViewer) {
      cesiumViewer.clock.multiplier = newSpeed;
    }
  });

  // 监听当前时间变化，同步Cesium时钟
  watch(currentTime, newTime => {
    if (cesiumViewer && newTime > 0) {
      cesiumViewer.clock.currentTime = Cesium.JulianDate.fromDate(new Date(newTime));
    }
  });

  // 组件卸载时清理定时器
  const cleanup = () => {
    if (playTimer) {
      clearInterval(playTimer);
    }
  };

  return {
    // 状态
    selectedVehicles,
    selectedDate,
    currentTime,
    isPlaying,
    playSpeed,
    trackData,
    offlineDevices,
    loading,

    // 计算属性
    timelinePercent,
    hasTrackData,

    // 方法
    handleReplay,
    handleReset,
    handleTimelineChange,
    handleTimelineDrag,
    togglePlayback,
    formatTime,
    handleMapReady,
    cleanup,

    // 时间相关
    startTime,
    endTime,
    totalDuration,
    timelineStartTime,
    timelineEndTime,
    showTimelineControl
  };
}
