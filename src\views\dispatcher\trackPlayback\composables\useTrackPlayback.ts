import { ref, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import type { TrackData, OfflineDevice, VehicleInfo, RawTrackDataResponse, Position } from "../types";
import { getDispatchTrackData } from "@/api/modules/dispatch";

export function useTrackPlayback() {
  // 响应式状态
  const selectedVehicles = ref<string[]>([]);
  const selectedDate = ref<string>("");
  const currentTime = ref<number>(0);
  const isPlaying = ref<boolean>(false);
  const playSpeed = ref<number>(1);
  const trackData = ref<TrackData[]>([]);
  const offlineDevices = ref<OfflineDevice[]>([]);
  const loading = ref<boolean>(false);
  const totalDuration = ref<number>(0);
  const startTime = ref<number>(0);
  const endTime = ref<number>(0);
  const showTimelineControl = ref<boolean>(false);

  // 播放定时器
  let playTimer: number | null = null;
  let cesiumViewer: any = null;

  // 计算属性
  const timelinePercent = computed({
    get: () => {
      if (totalDuration.value === 0) return 0;
      return ((currentTime.value - startTime.value) / totalDuration.value) * 100;
    },
    set: (percent: number) => {
      if (totalDuration.value > 0) {
        currentTime.value = startTime.value + (totalDuration.value * percent) / 100;
      }
    }
  });

  const hasTrackData = computed(() => trackData.value.length > 0);

  // 时间轴显示的开始和结束时间（基于选择的日期）
  const timelineStartTime = computed(() => {
    if (!selectedDate.value) return 0;
    const date = new Date(selectedDate.value);
    date.setHours(0, 0, 0, 0);
    return date.getTime();
  });

  const timelineEndTime = computed(() => {
    if (!selectedDate.value) return 0;
    const date = new Date(selectedDate.value);
    date.setHours(23, 59, 59, 999);
    return date.getTime();
  });

  // 格式化时间显示
  const formatTime = (timestamp: number): string => {
    if (!timestamp) return "00:00:00";
    const date = new Date(timestamp);
    return date.toLocaleTimeString("zh-CN", { hour12: false });
  };

  // 将新格式的轨迹数据转换为TrackData格式
  const convertRawDataToTrackData = (rawData: Record<string, string[]>): TrackData[] => {
    const trackDataList: TrackData[] = [];

    // 遍历每个设备的数据
    Object.entries(rawData).forEach(([deviceKey, trackPoints]) => {
      if (!trackPoints || trackPoints.length === 0) return;

      // 解析设备信息: deviceCode&deviceName
      const [deviceCode, deviceName] = deviceKey.split("&");

      // 转换位置数据
      const positions: Position[] = [];

      trackPoints.forEach((pointStr, index) => {
        try {
          // 解析轨迹点: "经度 纬度 海拔 朝向&时间&速度&故障码"
          const parts = pointStr.split("&");
          if (parts.length < 3) {
            console.warn(`轨迹点格式错误: ${pointStr}`);
            return;
          }

          // 解析坐标: "经度 纬度 海拔 朝向"
          const [longitude, latitude, altitude, heading] = parts[0].split(" ").map(Number);

          // 解析时间: 需要拼接选择的日期
          const time = parts[1]; // 如: "00:00:00"
          const fullDateTime = `${selectedDate.value} ${time}`;
          const timestamp = new Date(fullDateTime).getTime();

          // 解析速度
          const speed = parseFloat(parts[2]) || 0;

          // 解析故障码（可能为空）
          const faultCode = parts[3] || undefined;

          positions.push({
            timestamp,
            longitude,
            latitude,
            altitude: altitude || 0,
            heading: heading || 0,
            speed,
            faultCode // 添加故障码信息
          });
        } catch (error) {
          console.error(`解析轨迹点失败: ${pointStr}`, error);
        }
      });

      if (positions.length === 0) {
        console.warn(`设备 ${deviceCode} 没有有效的轨迹点`);
        return;
      }

      // 按时间排序
      positions.sort((a, b) => a.timestamp - b.timestamp);

      // 确定车辆类型（根据设备编码或名称判断）
      const vehicleType = deviceCode.includes("KK") || deviceName.includes("KK") ? "truck" : "excavator";

      trackDataList.push({
        id: deviceCode,
        name: deviceName,
        type: vehicleType,
        startTime: timelineStartTime.value, // 固定为选择日期的00:00:00
        endTime: timelineEndTime.value, // 固定为选择日期的23:59:59
        positions,
        status: "online"
      });
    });

    return trackDataList;
  };

  // 处理回放按钮点击
  const handleReplay = async () => {
    if (!selectedVehicles.value.length) {
      ElMessage.warning("请选择要回放的车辆");
      return;
    }

    if (!selectedDate.value) {
      ElMessage.warning("请选择回放日期");
      return;
    }

    loading.value = true;

    try {
      // 调用真实API
      const apiResponse = await getDispatchTrackData({
        deviceCodes: selectedVehicles.value.join(","),
        startTime: selectedDate.value + " 00:00:00",
        endTime: selectedDate.value + " 23:59:59"
      });

      // 转换数据格式
      const convertedTrackData = convertRawDataToTrackData(apiResponse.data as unknown as Record<string, string[]>);

      if (convertedTrackData.length === 0) {
        ElMessage.warning("轨迹数据转换失败");
        return;
      }

      // 设置轨迹数据
      trackData.value = convertedTrackData;

      // 设置固定的时间范围（选择日期的00:00:00到23:59:59）
      startTime.value = timelineStartTime.value;
      endTime.value = timelineEndTime.value;
      totalDuration.value = endTime.value - startTime.value;
      currentTime.value = startTime.value;

      // 更新离线设备
      updateOfflineDevices();

      // 显示时间轴控制
      showTimelineControl.value = true;

      ElMessage.success(`轨迹数据加载成功，共${convertedTrackData.length}个设备`);
    } catch (error) {
      console.error("加载轨迹数据失败:", error);
      ElMessage.error("加载轨迹数据失败");
    } finally {
      loading.value = false;
    }
  };

  // 处理重置按钮点击
  const handleReset = () => {
    // 停止播放
    if (isPlaying.value) {
      togglePlayback();
    }

    // 重置所有状态
    selectedVehicles.value = [];
    selectedDate.value = "";
    currentTime.value = 0;
    trackData.value = [];
    offlineDevices.value = [];
    totalDuration.value = 0;
    startTime.value = 0;
    endTime.value = 0;
    showTimelineControl.value = false;

    ElMessage.info("已重置所有设置");
  };

  // 处理时间轴变化
  const handleTimelineChange = (percent: number) => {
    timelinePercent.value = percent;
    updateOfflineDevices();

    // 同步Cesium时钟
    if (cesiumViewer) {
      cesiumViewer.clock.currentTime = Cesium.JulianDate.fromDate(new Date(currentTime.value));
    }
  };

  // 处理时间轴拖拽
  const handleTimelineDrag = (percent: number) => {
    timelinePercent.value = percent;
  };

  // 切换播放状态
  const togglePlayback = () => {
    if (!hasTrackData.value) return;

    isPlaying.value = !isPlaying.value;

    if (isPlaying.value) {
      startPlayback();
    } else {
      stopPlayback();
    }
  };

  // 开始播放
  const startPlayback = () => {
    if (playTimer) {
      clearInterval(playTimer);
    }

    // 根据播放倍速设置定时器间隔
    const interval = 1000 / playSpeed.value;

    playTimer = window.setInterval(() => {
      const nextTime = currentTime.value + 1000 * playSpeed.value;

      if (nextTime >= endTime.value) {
        // 播放结束
        currentTime.value = endTime.value;
        stopPlayback();
        ElMessage.info("轨迹回放完成");
      } else {
        currentTime.value = nextTime;
        updateOfflineDevices();
      }
    }, interval);

    // 同步Cesium播放状态
    if (cesiumViewer) {
      cesiumViewer.clock.shouldAnimate = true;
      cesiumViewer.clock.multiplier = playSpeed.value;
    }
  };

  // 停止播放
  const stopPlayback = () => {
    if (playTimer) {
      clearInterval(playTimer);
      playTimer = null;
    }

    isPlaying.value = false;

    // 同步Cesium播放状态
    if (cesiumViewer) {
      cesiumViewer.clock.shouldAnimate = false;
    }
  };

  // 更新离线设备列表
  const updateOfflineDevices = () => {
    if (!hasTrackData.value || currentTime.value === 0) {
      offlineDevices.value = [];
      return;
    }

    const offline: OfflineDevice[] = [];

    trackData.value.forEach(vehicle => {
      // 检查当前时间点车辆是否有位置数据
      const hasPositionAtCurrentTime = vehicle.positions.some(
        pos => Math.abs(pos.timestamp - currentTime.value) < 60000 // 1分钟内
      );

      if (!hasPositionAtCurrentTime) {
        offline.push({
          id: vehicle.id,
          name: vehicle.name,
          type: vehicle.type,
          offlineTime: currentTime.value,
          lastPosition: vehicle.positions[vehicle.positions.length - 1]
        });
      }
    });

    offlineDevices.value = offline;
  };

  // 处理地图准备就绪
  const handleMapReady = (viewer: any) => {
    cesiumViewer = viewer;
    console.log("地图初始化完成");
  };

  // 监听播放倍速变化
  watch(playSpeed, newSpeed => {
    if (isPlaying.value) {
      // 重新开始播放以应用新的倍速
      stopPlayback();
      isPlaying.value = true;
      startPlayback();
    }

    // 同步Cesium倍速
    if (cesiumViewer) {
      cesiumViewer.clock.multiplier = newSpeed;
    }
  });

  // 监听当前时间变化，同步Cesium时钟
  watch(currentTime, newTime => {
    if (cesiumViewer && newTime > 0) {
      cesiumViewer.clock.currentTime = Cesium.JulianDate.fromDate(new Date(newTime));
    }
  });

  // 组件卸载时清理定时器
  const cleanup = () => {
    if (playTimer) {
      clearInterval(playTimer);
    }
  };

  return {
    // 状态
    selectedVehicles,
    selectedDate,
    currentTime,
    isPlaying,
    playSpeed,
    trackData,
    offlineDevices,
    loading,

    // 计算属性
    timelinePercent,
    hasTrackData,

    // 方法
    handleReplay,
    handleReset,
    handleTimelineChange,
    handleTimelineDrag,
    togglePlayback,
    formatTime,
    handleMapReady,
    cleanup,

    // 时间相关
    startTime,
    endTime,
    totalDuration,
    timelineStartTime,
    timelineEndTime,
    showTimelineControl
  };
}
