// 轨迹回放相关类型定义

// 车辆类型
export type VehicleType = "excavator" | "truck";

// 车辆状态
export type VehicleStatus = "online" | "offline" | "working" | "idle";

// 位置信息
export interface Position {
  timestamp: number;
  longitude: number;
  latitude: number;
  altitude?: number;
  heading?: number;
  speed?: number;
  faultCode?: string; // 添加故障码字段
}

// 车辆信息
export interface VehicleInfo {
  id: string;
  name: string;
  type: VehicleType;
  status: VehicleStatus;
}

// 轨迹数据
export interface TrackData {
  id: string;
  name: string;
  type: VehicleType;
  startTime: number;
  endTime: number;
  positions: Position[];
  status?: VehicleStatus;
}

// 离线设备信息
export interface OfflineDevice {
  id: string;
  name: string;
  type: VehicleType;
  offlineTime: number;
  lastPosition?: Position;
}

// 播放控制状态
export interface PlaybackState {
  isPlaying: boolean;
  currentTime: number;
  playSpeed: number;
  totalDuration: number;
}

// 筛选条件
export interface FilterConditions {
  selectedVehicles: string[];
  selectedDate: string;
}

// 原始轨迹数据点（新的API返回格式）
// 数据格式: "经度 纬度 海拔 朝向&时间&速度&故障码"
export interface RawTrackDataResponse {
  jwtToken: string | null;
  message: string;
  statusCode: number;
  success: boolean;
  data: Record<string, string[]>; // key: deviceCode&deviceName, value: 轨迹点数组
}

// 时间范围
export interface TimeRange {
  start: number;
  end: number;
}
