<template>
  <div class="track-playback-container">
    <!-- 中间地图区域 -->
    <div class="map-container">
      <!-- 顶部控制区域 -->
      <div class="top-controls">
        <!-- 左上角筛选面板 -->
        <div class="filter-panel">
          <LASelect
            v-model="selectedVehicles"
            :fetch="getMineTrainListSelect"
            :replace-fields="{ key: 'code', label: 'name', value: 'code' }"
            :multiple="true"
            collapse-tags
            size="default"
            placeholder="请选择车辆"
            style="width: 200px"
          />
          <el-date-picker
            v-model="selectedDate"
            type="date"
            placeholder="选择日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            size="default"
            style="width: 160px"
          />
          <div class="filter-item">
            <el-button type="primary" @click="handleReplay" :loading="loading" size="default"> 回放 </el-button>
            <el-button @click="handleReset" size="default" style="margin-left: 4px">重置</el-button>
          </div>
        </div>

        <!-- 右上角离线设备面板 -->
        <div class="offline-panel">
          <div class="panel-title">离线设备</div>
          <div class="offline-devices">
            <div v-for="device in offlineDevices" :key="device.id" class="offline-device-item">
              {{ device.name }}
            </div>
            <div v-if="offlineDevices.length === 0" class="no-offline">暂无离线设备</div>
          </div>
        </div>
      </div>
      <MapComponent show-track-playback :track-playback-loader-props="trackPlaybackLoaderProps" @ready="handleMapReady" />
    </div>

    <!-- 底部时间轴控制 -->
    <TimelineControl
      :show-timeline-control="showTimelineControl"
      :current-time="currentTime"
      :is-playing="isPlaying"
      :play-speed="playSpeed"
      :has-data="hasTrackData"
      :start-time="timelineStartTime"
      :end-time="timelineEndTime"
      :timeline-percent="timelinePercent"
      @update:timeline-percent="timelinePercent = $event"
      @update:play-speed="playSpeed = $event"
      @timeline-change="handleTimelineChange"
      @timeline-drag="handleTimelineDrag"
      @toggle-playback="togglePlayback"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from "vue";
import { ElButton, ElDatePicker } from "element-plus";
import LASelect from "@/components/LASelect";
import MapComponent from "@/views/map/index.vue";
import TimelineControl from "./components/TimelineControl.vue";
import { useTrackPlayback } from "./composables/useTrackPlayback";
import { getMineTrainListSelect } from "@/api/modules/device";

// 使用组合式函数
const {
  // 状态
  selectedVehicles,
  selectedDate,
  currentTime,
  isPlaying,
  playSpeed,
  trackData,
  offlineDevices,
  loading,

  // 计算属性
  timelinePercent,
  hasTrackData,

  // 方法
  handleReplay,
  handleReset,
  handleTimelineChange,
  handleTimelineDrag,
  togglePlayback,
  handleMapReady,
  cleanup,

  // 时间相关
  startTime,
  endTime,
  totalDuration,
  timelineStartTime,
  timelineEndTime,
  showTimelineControl
} = useTrackPlayback();

// 根据轨迹数据生成车辆状态数据
const vehicleStatusData = computed(() => {
  const statusData: Record<string, any> = {};

  trackData.value.forEach(vehicle => {
    if (vehicle.positions && vehicle.positions.length > 0) {
      // 获取当前时间点最近的位置数据
      const currentPosition =
        vehicle.positions.find(
          pos => Math.abs(pos.timestamp - currentTime.value) < 5000 // 5秒内
        ) || vehicle.positions[0];

      // 根据车辆类型生成状态数据
      if (vehicle.type === "truck") {
        statusData[vehicle.id] = {
          name: vehicle.name,
          heavy: Math.random() > 0.5, // 随机载重状态，实际应从API获取
          speed: currentPosition.speed || 0,
          errorCode: Math.random() > 0.8 ? 4040 : undefined // 随机故障码
        };
      } else {
        statusData[vehicle.id] = {
          name: vehicle.name,
          speed: currentPosition.speed || 0
        };
      }
    }
  });

  return statusData;
});

// 轨迹回放加载器属性
const trackPlaybackLoaderProps = computed(() => ({
  trackData: trackData.value,
  currentTime: currentTime.value,
  isPlaying: isPlaying.value,
  playSpeed: playSpeed.value,
  show: hasTrackData.value,
  vehicleStatusData: vehicleStatusData.value
}));

// 车辆选项（根据实际设备编码）
const vehicleOptions = ref([
  { id: "LAKK-202501-1", name: "KK01", type: "truck" },
  { id: "LAKK-202501-2", name: "KK02", type: "truck" },
  { id: "LAWJ-202501-1", name: "WJ01", type: "excavator" },
  { id: "LAWJ-202501-2", name: "WJ02", type: "excavator" }
]);

onMounted(() => {
  // 初始化默认选择
  selectedDate.value = new Date().toISOString().split("T")[0];
});

onUnmounted(() => {
  // 清理资源
  cleanup();
});
</script>

<style scoped>
.track-playback-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  gap: 10px;
  padding: 10px;
  overflow: hidden;
}

.map-container {
  flex: 1;
  display: flex;
  position: relative;
  background: #23245a;
  min-height: 0;
}

.top-controls {
  position: absolute;
  top: 16px;
  left: 16px;
  right: 16px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  z-index: 10;
  pointer-events: none;
}

.filter-panel {
  display: flex;
  align-items: center;
  gap: 4px;
  pointer-events: auto;
}

.filter-item {
  display: flex;
  align-items: center;
}

.offline-panel {
  min-width: 200px;
  max-width: 300px;
  background: rgba(27, 28, 70, 1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 12px;
  pointer-events: auto;
}

.panel-title {
  font-weight: 600;
  color: rgba(255, 255, 255, 1);
  margin-bottom: 8px;
  font-size: 14px;
}

.offline-devices {
  max-height: 120px;
  overflow-y: auto;
}

.offline-device-item {
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  margin-bottom: 4px;
  font-size: 12px;
  color: rgba(255, 255, 255, 1);
}

.no-offline {
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  padding: 8px;
}
</style>
