import type { TrackData, Position } from "../types";
import { getDispatchTrackData } from "@/api/modules/dispatch";

// 生成模拟轨迹点
function generateTrackPositions(
  startLng: number,
  startLat: number,
  endLng: number,
  endLat: number,
  startTime: number,
  duration: number,
  pointCount: number = 60
): Position[] {
  const positions: Position[] = [];

  for (let i = 0; i < pointCount; i++) {
    const progress = i / (pointCount - 1);
    const timestamp = startTime + duration * progress;

    // 简单的线性插值
    const lng = startLng + (endLng - startLng) * progress;
    const lat = startLat + (endLat - startLat) * progress;

    // 添加一些随机偏移模拟真实轨迹
    const lngOffset = (Math.random() - 0.5) * 0.001;
    const latOffset = (Math.random() - 0.5) * 0.001;

    positions.push({
      timestamp,
      longitude: lng + lngOffset,
      latitude: lat + latOffset,
      altitude: 100 + Math.random() * 50,
      heading: Math.random() * 360,
      speed: 20 + Math.random() * 30
    });
  }

  return positions;
}

// 当前时间
const now = Date.now();
const oneHour = 60 * 60 * 1000;
const startTime = now - 2 * oneHour; // 2小时前开始
const duration = 3 * oneHour; // 持续3小时

// 模拟轨迹数据
export const mockTrackData: TrackData[] = [
  {
    id: "excavator_001",
    name: "KK-01 挖机",
    type: "excavator",
    startTime,
    endTime: startTime + duration,
    positions: generateTrackPositions(
      108.0,
      34.0, // 起点
      108.02,
      34.01, // 终点
      startTime,
      duration,
      80
    )
  },
  {
    id: "excavator_002",
    name: "KK-02 挖机",
    type: "excavator",
    startTime: startTime + 30 * 60 * 1000, // 30分钟后开始
    endTime: startTime + duration,
    positions: generateTrackPositions(108.01, 34.005, 108.025, 34.015, startTime + 30 * 60 * 1000, duration - 30 * 60 * 1000, 70)
  },
  {
    id: "truck_001",
    name: "WJ-01 矿车",
    type: "truck",
    startTime,
    endTime: startTime + duration,
    positions: generateTrackPositions(108.005, 34.002, 108.03, 34.02, startTime, duration, 100)
  },
  {
    id: "truck_002",
    name: "WJ-02 矿车",
    type: "truck",
    startTime,
    endTime: startTime + duration - 60 * 60 * 1000, // 提前1小时结束（模拟离线）
    positions: generateTrackPositions(108.008, 34.003, 108.028, 34.018, startTime, duration - 60 * 60 * 1000, 80)
  },
  {
    id: "truck_003",
    name: "WJ-03 矿车",
    type: "truck",
    startTime: startTime + 45 * 60 * 1000, // 45分钟后开始
    endTime: startTime + duration,
    positions: generateTrackPositions(108.012, 34.008, 108.035, 34.025, startTime + 45 * 60 * 1000, duration - 45 * 60 * 1000, 90)
  }
];

// 模拟API调用
export async function fetchTrackData(deviceCodes: string[], startTime: string, endTime: string): Promise<TrackData[]> {
  const res = await getDispatchTrackData({
    deviceCodes,
    startTime,
    endTime
  });
  console.log(res);
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 800 + Math.random() * 400));

  // 根据选择的车辆ID过滤数据
  const filteredData = mockTrackData.filter(item => deviceCodes.includes(item.id));

  // 模拟偶尔的网络错误
  if (Math.random() < 0.1) {
    throw new Error("网络请求失败");
  }

  return filteredData;
}
